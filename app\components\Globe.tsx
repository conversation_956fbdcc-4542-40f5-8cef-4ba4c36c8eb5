'use client';

import { useState } from 'react';
import { oceanFeeds, type OceanFeed } from '../data/oceanFeeds';

interface GlobeProps {
  onFeedSelect: (feed: OceanFeed) => void;
}

export default function Globe({ onFeedSelect }: GlobeProps) {
  const [selectedFeedId, setSelectedFeedId] = useState<string | null>(null);

  const handleMarkerClick = (feed: OceanFeed) => {
    setSelectedFeedId(feed.id);
    onFeedSelect(feed);
  };

  // Simple world map with clickable markers - fallback implementation
  return (
    <div className="relative w-full h-full bg-gradient-to-b from-blue-900 via-blue-800 to-blue-900 overflow-hidden">
      {/* World Map Background */}
      <div className="absolute inset-0 opacity-20">
        <svg
          viewBox="0 0 1000 500"
          className="w-full h-full"
          style={{ filter: 'drop-shadow(0 0 10px rgba(6, 182, 212, 0.3))' }}
        >
          {/* Simplified world map paths */}
          <path
            d="M200,200 L300,180 L400,190 L500,200 L600,180 L700,190 L800,200"
            stroke="rgba(6, 182, 212, 0.5)"
            strokeWidth="2"
            fill="none"
          />
          <path
            d="M150,250 L250,240 L350,250 L450,240 L550,250 L650,240 L750,250 L850,240"
            stroke="rgba(6, 182, 212, 0.5)"
            strokeWidth="2"
            fill="none"
          />
          <path
            d="M100,300 L200,290 L300,300 L400,290 L500,300 L600,290 L700,300 L800,290 L900,300"
            stroke="rgba(6, 182, 212, 0.5)"
            strokeWidth="2"
            fill="none"
          />
        </svg>
      </div>

      {/* Ocean Feed Markers */}
      <div className="absolute inset-0">
        {oceanFeeds.map((feed) => {
          // Convert lat/lng to screen coordinates (simplified projection)
          const x = ((feed.longitude + 180) / 360) * 100;
          const y = ((90 - feed.latitude) / 180) * 100;

          return (
            <button
              key={feed.id}
              onClick={() => handleMarkerClick(feed)}
              className={`absolute transform -translate-x-1/2 -translate-y-1/2 transition-all duration-200 hover:scale-125 ${
                selectedFeedId === feed.id ? 'scale-125' : ''
              }`}
              style={{
                left: `${x}%`,
                top: `${y}%`,
              }}
              title={feed.name}
            >
              <div className="relative">
                {/* Pulsing ring animation */}
                <div className="absolute inset-0 rounded-full bg-cyan-400 animate-ping opacity-75"></div>

                {/* Main marker */}
                <div className="relative w-4 h-4 bg-cyan-400 rounded-full border-2 border-white shadow-lg">
                  <div className="absolute inset-1 bg-cyan-200 rounded-full"></div>
                </div>

                {/* Tooltip on hover */}
                <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 hover:opacity-100 transition-opacity whitespace-nowrap pointer-events-none">
                  {feed.name}
                  <div className="text-cyan-300">{feed.region}</div>
                </div>
              </div>
            </button>
          );
        })}
      </div>

      {/* Grid overlay for ocean feel */}
      <div className="absolute inset-0 opacity-10">
        <div className="w-full h-full" style={{
          backgroundImage: `
            linear-gradient(rgba(6, 182, 212, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(6, 182, 212, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px'
        }}></div>
      </div>

      {/* Animated waves */}
      <div className="absolute bottom-0 left-0 right-0 h-20 overflow-hidden">
        <div className="absolute inset-0 opacity-30">
          <div className="wave-animation absolute inset-0 bg-gradient-to-t from-cyan-500/20 to-transparent"></div>
          <div className="wave-animation absolute inset-0 bg-gradient-to-t from-blue-500/20 to-transparent" style={{ animationDelay: '0.5s' }}></div>
        </div>
      </div>

      {/* Legend */}
      <div className="absolute top-4 right-4 bg-black/50 backdrop-blur-sm rounded-lg p-4 text-white">
        <h3 className="font-semibold mb-2">Legend</h3>
        <div className="flex items-center space-x-2 text-sm">
          <div className="w-3 h-3 rounded-full bg-cyan-400"></div>
          <span>Live Ocean Feed</span>
        </div>
        <p className="text-xs text-blue-200 mt-2">
          {oceanFeeds.length} feeds available
        </p>
      </div>

      {/* Controls hint */}
      <div className="absolute bottom-20 right-4 bg-black/50 backdrop-blur-sm rounded-lg p-3 text-white text-xs max-w-48">
        <p className="mb-1">🖱️ Click markers to view feeds</p>
        <p className="mb-1">🌊 Explore ocean life worldwide</p>
        <p>📍 {oceanFeeds.length} live feeds available</p>
      </div>
    </div>
  );
}
