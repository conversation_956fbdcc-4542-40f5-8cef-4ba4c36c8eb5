'use client';

import { useEffect, useRef, useState } from 'react';
import { Deck } from '@deck.gl/core';
import { ScatterplotLayer } from '@deck.gl/layers';
import { _GlobeView as GlobeView } from '@deck.gl/core';
import { oceanFeeds, type OceanFeed } from '../data/oceanFeeds';

interface GlobeProps {
  onFeedSelect: (feed: OceanFeed) => void;
}

export default function Globe({ onFeedSelect }: GlobeProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const deckRef = useRef<Deck | null>(null);
  const [viewState, setViewState] = useState({
    longitude: 0,
    latitude: 20,
    zoom: 0,
    pitch: 0,
    bearing: 0
  });

  useEffect(() => {
    if (!containerRef.current) return;

    // Create the deck.gl instance
    const deck = new Deck({
      container: containerRef.current,
      views: [new GlobeView()],
      initialViewState: viewState,
      controller: true,
      onViewStateChange: ({ viewState }) => {
        setViewState(viewState);
      },
      layers: [
        // Ocean feeds layer
        new ScatterplotLayer({
          id: 'ocean-feeds',
          data: oceanFeeds,
          getPosition: (d: OceanFeed) => [d.longitude, d.latitude, 0],
          getRadius: 50000,
          getFillColor: [0, 200, 255, 200],
          getLineColor: [255, 255, 255, 255],
          lineWidthMinPixels: 2,
          radiusMinPixels: 8,
          radiusMaxPixels: 20,
          pickable: true,
          onClick: (info) => {
            if (info.object) {
              onFeedSelect(info.object as OceanFeed);
            }
          },
          updateTriggers: {
            getFillColor: viewState.zoom
          }
        })
      ],
      parameters: {
        clearColor: [0.1, 0.2, 0.4, 1.0]
      }
    });

    deckRef.current = deck;

    return () => {
      deck.finalize();
    };
  }, [onFeedSelect, viewState]);

  return (
    <div className="relative w-full h-full">
      <div 
        ref={containerRef} 
        className="w-full h-full cursor-grab active:cursor-grabbing"
        style={{ background: 'radial-gradient(circle, #1a365d 0%, #0f172a 100%)' }}
      />
      
      {/* Loading overlay */}
      <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
        <div className="text-white text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-400 mx-auto mb-4"></div>
          <p className="text-sm opacity-75">Loading Ocean Globe...</p>
        </div>
      </div>

      {/* Legend */}
      <div className="absolute top-4 right-4 bg-black/50 backdrop-blur-sm rounded-lg p-4 text-white">
        <h3 className="font-semibold mb-2">Legend</h3>
        <div className="flex items-center space-x-2 text-sm">
          <div className="w-3 h-3 rounded-full bg-cyan-400"></div>
          <span>Live Ocean Feed</span>
        </div>
        <p className="text-xs text-blue-200 mt-2">
          {oceanFeeds.length} feeds available
        </p>
      </div>

      {/* Controls hint */}
      <div className="absolute bottom-20 right-4 bg-black/50 backdrop-blur-sm rounded-lg p-3 text-white text-xs max-w-48">
        <p className="mb-1">🖱️ Drag to rotate</p>
        <p className="mb-1">🔍 Scroll to zoom</p>
        <p>📍 Click markers for feeds</p>
      </div>
    </div>
  );
}
