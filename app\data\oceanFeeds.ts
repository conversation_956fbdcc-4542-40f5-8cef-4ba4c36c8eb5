export interface OceanFeed {
  id: string;
  name: string;
  latitude: number;
  longitude: number;
  stream_url: string;
  depth: string;
  region: string;
  source: string;
  description: string;
  zone: string;
  species: string[];
  educational_content: {
    zone_info: string;
    common_species: string;
    interesting_facts: string[];
  };
}

export const oceanFeeds: OceanFeed[] = [
  {
    id: "nautilus-gulf",
    name: "Nautilus - Gulf of Mexico",
    latitude: 27.5,
    longitude: -90.2,
    stream_url: "https://www.youtube.com/embed/jzx_n25g3kA?autoplay=1&mute=0&controls=1&origin=https%3A%2F%2Fexplore.org&playsinline=1&showinfo=1&rel=0&iv_load_policy=3&modestbranding=0&enablejsapi=1&widgetid=1&forigin=https%3A%2F%2Fexplore.org%2Flivecams%2Foceans%2Futopia-village-reef-channel&aoriginsup=1&gporigin=https%3A%2F%2Fchatgpt.com%2F&vf=3",
    depth: "1,200m",
    region: "Gulf of Mexico",
    source: "Ocean Exploration Trust",
    description: "Currently exploring deep-sea coral formations and invertebrates in the Gulf.",
    zone: "Bathypelagic",
    species: ["Deep-sea corals", "Anglerfish", "Giant isopods", "Sea spiders"],
    educational_content: {
      zone_info: "The bathypelagic zone (1,000-4,000m) is known as the midnight zone where no sunlight penetrates.",
      common_species: "Bioluminescent creatures, deep-sea corals, and specialized predators adapted to extreme pressure.",
      interesting_facts: [
        "Pressure is 100 times greater than at sea level",
        "Temperature remains near freezing (2-4°C)",
        "Many creatures create their own light through bioluminescence"
      ]
    }
  },
  
];

export const getRandomFeed = (): OceanFeed => {
  return oceanFeeds[Math.floor(Math.random() * oceanFeeds.length)];
};

export const getFeedById = (id: string): OceanFeed | undefined => {
  return oceanFeeds.find(feed => feed.id === id);
};

export const getFeedsByZone = (zone: string): OceanFeed[] => {
  return oceanFeeds.filter(feed => feed.zone === zone);
};
