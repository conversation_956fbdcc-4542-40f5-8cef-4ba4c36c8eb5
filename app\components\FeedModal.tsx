'use client';

import { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { type OceanFeed } from '../data/oceanFeeds';

interface FeedModalProps {
  feed: OceanFeed | null;
  isOpen: boolean;
  onClose: () => void;
}

export default function FeedModal({ feed, isOpen, onClose }: FeedModalProps) {
  if (!feed) return null;

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black/75 backdrop-blur-sm" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-6xl transform overflow-hidden rounded-2xl bg-gradient-to-br from-blue-900 to-blue-800 p-6 text-left align-middle shadow-xl transition-all">
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <Dialog.Title as="h3" className="text-2xl font-bold text-white">
                    {feed.name}
                  </Dialog.Title>
                  <button
                    type="button"
                    className="rounded-md bg-white/10 p-2 text-white hover:bg-white/20 transition-colors"
                    onClick={onClose}
                  >
                    <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                  </button>
                </div>

                <div className="grid lg:grid-cols-3 gap-6">
                  {/* Video Player */}
                  <div className="lg:col-span-2">
                    <div className="aspect-video rounded-lg overflow-hidden bg-black mb-4">
                      <iframe
                        src={feed.stream_url}
                        className="w-full h-full"
                        frameBorder="0"
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                        allowFullScreen
                        title={feed.name}
                      ></iframe>
                    </div>
                    
                    {/* Stream Info */}
                    <div className="bg-white/10 rounded-lg p-4">
                      <h4 className="text-lg font-semibold text-white mb-2">Stream Information</h4>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-blue-300 font-medium">Location:</span>
                          <p className="text-white">{feed.region}</p>
                        </div>
                        <div>
                          <span className="text-blue-300 font-medium">Depth:</span>
                          <p className="text-white">{feed.depth}</p>
                        </div>
                        <div>
                          <span className="text-blue-300 font-medium">Source:</span>
                          <p className="text-white">{feed.source}</p>
                        </div>
                        <div>
                          <span className="text-blue-300 font-medium">Ocean Zone:</span>
                          <p className="text-white">{feed.zone}</p>
                        </div>
                      </div>
                      <div className="mt-4">
                        <span className="text-blue-300 font-medium">Description:</span>
                        <p className="text-white mt-1">{feed.description}</p>
                      </div>
                    </div>
                  </div>

                  {/* Educational Panel */}
                  <div className="space-y-4">
                    {/* Marine Life */}
                    <div className="bg-white/10 rounded-lg p-4">
                      <h4 className="text-lg font-semibold text-white mb-3">Marine Life</h4>
                      <div className="space-y-2">
                        {feed.species.map((species, index) => (
                          <div
                            key={index}
                            className="bg-cyan-500/20 text-cyan-200 px-3 py-2 rounded-lg text-sm"
                          >
                            {species}
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Ocean Zone Info */}
                    <div className="bg-white/10 rounded-lg p-4">
                      <h4 className="text-lg font-semibold text-white mb-3">
                        About the {feed.zone} Zone
                      </h4>
                      <p className="text-blue-200 text-sm mb-3">
                        {feed.educational_content.zone_info}
                      </p>
                      <p className="text-blue-200 text-sm">
                        {feed.educational_content.common_species}
                      </p>
                    </div>

                    {/* Interesting Facts */}
                    <div className="bg-white/10 rounded-lg p-4">
                      <h4 className="text-lg font-semibold text-white mb-3">Did You Know?</h4>
                      <ul className="space-y-2">
                        {feed.educational_content.interesting_facts.map((fact, index) => (
                          <li key={index} className="text-blue-200 text-sm flex items-start">
                            <span className="text-cyan-400 mr-2">•</span>
                            {fact}
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Coordinates */}
                    <div className="bg-white/10 rounded-lg p-4">
                      <h4 className="text-lg font-semibold text-white mb-2">Coordinates</h4>
                      <div className="text-sm text-blue-200">
                        <p>Latitude: {feed.latitude.toFixed(4)}°</p>
                        <p>Longitude: {feed.longitude.toFixed(4)}°</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Footer */}
                <div className="mt-6 flex justify-end">
                  <button
                    type="button"
                    className="bg-cyan-500 hover:bg-cyan-600 text-white px-6 py-2 rounded-lg font-medium transition-colors"
                    onClick={onClose}
                  >
                    Close
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
