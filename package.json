{"name": "oceaneye", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@deck.gl/core": "^9.1.13", "@deck.gl/layers": "^9.1.13", "@deck.gl/react": "^9.1.13", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "deck.gl": "^9.1.13", "mapbox-gl": "^3.13.0", "next": "15.4.1", "react": "19.1.0", "react-dom": "19.1.0", "react-map-gl": "^8.0.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.1", "tailwindcss": "^4", "typescript": "^5"}}